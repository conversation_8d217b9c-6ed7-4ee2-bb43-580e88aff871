@extends('layouts.admin')

@section('title', 'Create Assignment')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create Assignment</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.grading.assignments.index') }}">Assignments</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.grading.assignments.index') }}" class="btn btn-white btn-sm">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Assignment Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.grading.assignments.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Assignment Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title') }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="assignment_code" class="form-label">Assignment Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('assignment_code') is-invalid @enderror" 
                                           id="assignment_code" name="assignment_code" value="{{ old('assignment_code') }}" required>
                                    @error('assignment_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instructions</label>
                            <textarea class="form-control @error('instructions') is-invalid @enderror" 
                                      id="instructions" name="instructions" rows="4">{{ old('instructions') }}</textarea>
                            @error('instructions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academic_year_id" class="form-label">Academic Year <span class="text-danger">*</span></label>
                                    <select class="form-select @error('academic_year_id') is-invalid @enderror" 
                                            id="academic_year_id" name="academic_year_id" required>
                                        <option value="">Select Academic Year</option>
                                        @foreach($academicYears ?? [] as $year)
                                            <option value="{{ $year->id }}" {{ old('academic_year_id') == $year->id ? 'selected' : '' }}>
                                                {{ $year->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('academic_year_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academic_term_id" class="form-label">Academic Term <span class="text-danger">*</span></label>
                                    <select class="form-select @error('academic_term_id') is-invalid @enderror" 
                                            id="academic_term_id" name="academic_term_id" required>
                                        <option value="">Select Academic Term</option>
                                        @foreach($academicTerms ?? [] as $term)
                                            <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>
                                                {{ $term->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('academic_term_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="grade_category_id" class="form-label">Grade Category <span class="text-danger">*</span></label>
                                    <select class="form-select @error('grade_category_id') is-invalid @enderror" 
                                            id="grade_category_id" name="grade_category_id" required>
                                        <option value="">Select Grade Category</option>
                                        @foreach($gradeCategories ?? [] as $category)
                                            <option value="{{ $category->id }}" {{ old('grade_category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }} ({{ $category->weight_percentage }}%)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('grade_category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subject_id" class="form-label">Subject <span class="text-danger">*</span></label>
                                    <select class="form-select @error('subject_id') is-invalid @enderror" 
                                            id="subject_id" name="subject_id" required>
                                        <option value="">Select Subject</option>
                                        @foreach($subjects ?? [] as $subject)
                                            <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                                {{ $subject->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('subject_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                            <select class="form-select @error('class_id') is-invalid @enderror" 
                                    id="class_id" name="class_id" required>
                                <option value="">Select Class</option>
                                @foreach($classRooms ?? [] as $class)
                                    <option value="{{ $class->id }}" {{ old('class_id') == $class->id ? 'selected' : '' }}>
                                        {{ $class->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('class_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="total_marks" class="form-label">Total Marks <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('total_marks') is-invalid @enderror" 
                                           id="total_marks" name="total_marks" value="{{ old('total_marks') }}" 
                                           min="0" step="0.01" required>
                                    @error('total_marks')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="passing_marks" class="form-label">Passing Marks <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('passing_marks') is-invalid @enderror" 
                                           id="passing_marks" name="passing_marks" value="{{ old('passing_marks') }}" 
                                           min="0" step="0.01" required>
                                    @error('passing_marks')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="assigned_date" class="form-label">Assigned Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('assigned_date') is-invalid @enderror" 
                                           id="assigned_date" name="assigned_date" value="{{ old('assigned_date') }}" required>
                                    @error('assigned_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('due_date') is-invalid @enderror" 
                                           id="due_date" name="due_date" value="{{ old('due_date') }}" required>
                                    @error('due_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="submission_type" class="form-label">Submission Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('submission_type') is-invalid @enderror" 
                                            id="submission_type" name="submission_type" required>
                                        <option value="">Select Submission Type</option>
                                        <option value="online" {{ old('submission_type') == 'online' ? 'selected' : '' }}>Online</option>
                                        <option value="offline" {{ old('submission_type') == 'offline' ? 'selected' : '' }}>Offline</option>
                                        <option value="both" {{ old('submission_type') == 'both' ? 'selected' : '' }}>Both</option>
                                    </select>
                                    @error('submission_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('status') is-invalid @enderror" 
                                            id="status" name="status" required>
                                        <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                        <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="late_penalty_percentage" class="form-label">Late Penalty (%)</label>
                                    <input type="number" class="form-control @error('late_penalty_percentage') is-invalid @enderror" 
                                           id="late_penalty_percentage" name="late_penalty_percentage" 
                                           value="{{ old('late_penalty_percentage') }}" min="0" max="100" step="0.01">
                                    @error('late_penalty_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_attempts" class="form-label">Max Attempts</label>
                                    <input type="number" class="form-control @error('max_attempts') is-invalid @enderror" 
                                           id="max_attempts" name="max_attempts" value="{{ old('max_attempts') }}" min="1">
                                    @error('max_attempts')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="allow_late_submission" 
                                               name="allow_late_submission" value="1" 
                                               {{ old('allow_late_submission') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="allow_late_submission">
                                            Allow Late Submission
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_published" 
                                               name="is_published" value="1" 
                                               {{ old('is_published') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_published">
                                            Publish Assignment
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.grading.assignments.index') }}" class="btn btn-white">
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Create Assignment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i> Assignment Guidelines</h6>
                        <ul class="mb-0 small">
                            <li>Choose a descriptive title for the assignment</li>
                            <li>Use a unique assignment code for identification</li>
                            <li>Set realistic due dates</li>
                            <li>Provide clear instructions for students</li>
                            <li>Select appropriate grade category and subject</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i> Important Notes</h6>
                        <ul class="mb-0 small">
                            <li>Assignment code must be unique</li>
                            <li>Due date must be after assigned date</li>
                            <li>Passing marks cannot exceed total marks</li>
                            <li>Published assignments will be visible to students</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate assignment code from title
    $('#title').on('input', function() {
        let title = $(this).val();
        let code = title.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10);
        $('#assignment_code').val(code);
    });

    // Validate passing marks against total marks
    $('#total_marks, #passing_marks').on('input', function() {
        let totalMarks = parseFloat($('#total_marks').val()) || 0;
        let passingMarks = parseFloat($('#passing_marks').val()) || 0;
        
        if (passingMarks > totalMarks) {
            $('#passing_marks').addClass('is-invalid');
            $('#passing_marks').siblings('.invalid-feedback').text('Passing marks cannot exceed total marks');
        } else {
            $('#passing_marks').removeClass('is-invalid');
        }
    });

    // Validate due date against assigned date
    $('#assigned_date, #due_date').on('change', function() {
        let assignedDate = new Date($('#assigned_date').val());
        let dueDate = new Date($('#due_date').val());
        
        if (assignedDate && dueDate && dueDate < assignedDate) {
            $('#due_date').addClass('is-invalid');
            $('#due_date').siblings('.invalid-feedback').text('Due date must be after assigned date');
        } else {
            $('#due_date').removeClass('is-invalid');
        }
    });
});
</script>
@endpush
