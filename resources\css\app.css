@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';

    /* Custom colors for school management system */
    --color-primary-50: #eff6ff;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;

    --color-secondary-50: #f8fafc;
    --color-secondary-500: #64748b;
    --color-secondary-600: #475569;
    --color-secondary-700: #334155;
}

/* Custom component styles */
@layer components {
    .toast {
        @apply fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out;
    }

    .toast.success {
        @apply bg-green-500 text-white;
    }

    .toast.error {
        @apply bg-red-500 text-white;
    }

    .toast.warning {
        @apply bg-yellow-500 text-white;
    }

    .toast.info {
        @apply bg-blue-500 text-white;
    }

    .btn-primary {
        @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center;
    }

    .btn-secondary {
        @apply bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center shadow-sm;
    }

    .btn-cancel {
        @apply bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center shadow-sm;
    }

    .card {
        @apply bg-white rounded-lg shadow-md p-6;
    }

    .form-input {
        @apply block w-full rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none bg-white px-4 py-3 transition-colors duration-200;
    }

    .form-select {
        @apply block w-full rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none bg-white px-4 py-3 pr-12 transition-colors duration-200;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 1rem center;
        background-repeat: no-repeat;
        background-size: 1.25em 1.25em;
    }

    /* Firefox specific styling for consistent dropdown appearance */
    @-moz-document url-prefix() {
        .form-select {
            background-position: right 1rem center;
        }
    }

    .form-textarea {
        @apply block w-full rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none bg-white px-4 py-3 transition-colors duration-200 resize-y;
    }

    /* Date input styling */
    input[type="date"] {
        @apply cursor-pointer;
    }

    /* Remove borders from calendar popup */
    input[type="date"]::-webkit-calendar-picker-indicator {
        @apply cursor-pointer;
    }

    /* Remove calendar popup borders */
    input[type="date"]::-webkit-datetime-edit {
        @apply border-0;
    }

    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-clear-button {
        @apply hidden;
    }

    /* Hide calendar popup border for all browsers */
    input[type="date"]::-webkit-calendar-picker-indicator:hover {
        @apply bg-gray-100 rounded;
    }

    /* Additional calendar popup styling */
    input[type="date"]::-webkit-datetime-edit-fields-wrapper {
        @apply border-0;
    }

    input[type="date"]::-webkit-datetime-edit-text {
        @apply border-0;
    }

    input[type="date"]::-webkit-datetime-edit-month-field,
    input[type="date"]::-webkit-datetime-edit-day-field,
    input[type="date"]::-webkit-datetime-edit-year-field {
        @apply border-0;
    }

    /* Remove any remaining borders from calendar */
    input[type="date"]:focus::-webkit-datetime-edit {
        @apply border-0 outline-none;
    }

    .sidebar-link {
        @apply flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200;
    }

    .sidebar-link.active {
        @apply bg-blue-100 text-blue-700;
    }

    .sidebar-link:hover {
        @apply bg-gray-100 text-gray-900;
    }

    .stat-card {
        @apply bg-white overflow-hidden shadow rounded-lg;
    }

    .stat-card-content {
        @apply p-5;
    }

    .stat-card-icon {
        @apply w-8 h-8 rounded-md flex items-center justify-center;
    }

    .stat-card-value {
        @apply text-lg font-medium text-gray-900;
    }

    .stat-card-label {
        @apply text-sm font-medium text-gray-500 truncate;
    }

    .grid-stats {
        @apply grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4;
    }

    .grid-stats-3 {
        @apply grid grid-cols-1 md:grid-cols-3 gap-4;
    }

    .grid-stats-2 {
        @apply grid grid-cols-1 md:grid-cols-2 gap-4;
    }

    /* Search and filter styles */
    .search-input {
        @apply block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
    }

    .filter-button {
        @apply inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
    }

    /* Improved spacing utilities */
    .sidebar-spacing {
        @apply transition-all duration-300;
    }

    /* Card hover effects */
    .card-hover {
        @apply hover:shadow-md transition-shadow duration-200;
    }

    /* Statistics grid for 6 columns */
    .grid-stats-4 {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
    }

    .grid-stats-6 {
        @apply grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4;
    }

    .grid-stats-8 {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
    }

    /* Filterable item styles */
    [data-filterable] {
        @apply transition-opacity duration-200;
    }

    [data-filterable][style*="display: none"] {
        @apply opacity-0;
    }

    /* Fixed layout to prevent content shifting */
    .main-content-fixed {
        @apply ml-64 transition-none;
    }

    .main-content-collapsed {
        @apply ml-16 transition-none;
    }

    /* Less rounded badges */
    .badge {
        @apply inline-flex px-2 py-1 text-xs font-semibold rounded-md;
    }

    .badge-yellow {
        @apply bg-yellow-100 text-yellow-800;
    }

    .badge-green {
        @apply bg-green-100 text-green-800;
    }

    .badge-red {
        @apply bg-red-100 text-red-800;
    }

    .badge-blue {
        @apply bg-blue-100 text-blue-800;
    }

    .badge-purple {
        @apply bg-purple-100 text-purple-800;
    }

    .badge-gray {
        @apply bg-gray-100 text-gray-800;
    }

    .badge-orange {
        @apply bg-orange-100 text-orange-800;
    }

    .badge-indigo {
        @apply bg-indigo-100 text-indigo-800;
    }

    /* Notification counter */
    .notification-badge {
        @apply absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center;
    }

    /* Custom modal styles - using inline styles in component now */

    /* Dropdown styling improvements */
    .dropdown-menu {
        @apply bg-white rounded-lg shadow-lg border-0 p-2;
    }

    .dropdown-item {
        @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200;
    }

    /* Form select dropdown chevron positioning - properly positioned chevron */
    .form-input[type="select"],
    select.form-input {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.75rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }

    /* Improved dropdown spacing for pagination and other small selects */
    select[id*="per_page"],
    select.pagination-select {
        @apply px-3 py-2 text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-200 focus:outline-none;
    }

    /* Ensure dropdown options have proper spacing */
    select option {
        @apply py-1 px-2;
    }

    /* Toast notification improvements */
    .toast-container {
        @apply fixed bottom-4 right-4 z-50 space-y-2;
    }

    .toast-success {
        @apply bg-green-500 text-white p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out;
    }

    .toast-error {
        @apply bg-red-500 text-white p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out;
    }

    .toast-warning {
        @apply bg-yellow-500 text-white p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out;
    }

    .toast-info {
        @apply bg-blue-500 text-white p-4 rounded-lg shadow-lg transform transition-all duration-300 ease-in-out;
    }
}
