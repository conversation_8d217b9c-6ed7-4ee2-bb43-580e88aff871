@extends('layouts.app')

@section('title', 'Create Exam')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create Exam</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.grading.exams.index') }}">Exams</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.grading.exams.index') }}" class="btn btn-white btn-sm">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Exam Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.grading.exams.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Exam Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title') }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="exam_code" class="form-label">Exam Code <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('exam_code') is-invalid @enderror" 
                                           id="exam_code" name="exam_code" value="{{ old('exam_code') }}" required>
                                    @error('exam_code')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academic_year_id" class="form-label">Academic Year <span class="text-danger">*</span></label>
                                    <select class="form-select @error('academic_year_id') is-invalid @enderror" 
                                            id="academic_year_id" name="academic_year_id" required>
                                        <option value="">Select Academic Year</option>
                                        @foreach($academicYears ?? [] as $year)
                                            <option value="{{ $year->id }}" {{ old('academic_year_id') == $year->id ? 'selected' : '' }}>
                                                {{ $year->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('academic_year_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="academic_term_id" class="form-label">Academic Term <span class="text-danger">*</span></label>
                                    <select class="form-select @error('academic_term_id') is-invalid @enderror" 
                                            id="academic_term_id" name="academic_term_id" required>
                                        <option value="">Select Academic Term</option>
                                        @foreach($academicTerms ?? [] as $term)
                                            <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>
                                                {{ $term->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('academic_term_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="grade_category_id" class="form-label">Grade Category <span class="text-danger">*</span></label>
                                    <select class="form-select @error('grade_category_id') is-invalid @enderror" 
                                            id="grade_category_id" name="grade_category_id" required>
                                        <option value="">Select Grade Category</option>
                                        @foreach($gradeCategories ?? [] as $category)
                                            <option value="{{ $category->id }}" {{ old('grade_category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }} ({{ $category->weight_percentage }}%)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('grade_category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="grade_scale_id" class="form-label">Grade Scale</label>
                                    <select class="form-select @error('grade_scale_id') is-invalid @enderror" 
                                            id="grade_scale_id" name="grade_scale_id">
                                        <option value="">Use Default Grade Scale</option>
                                        @foreach($gradeScales ?? [] as $scale)
                                            <option value="{{ $scale->id }}" {{ old('grade_scale_id') == $scale->id ? 'selected' : '' }}>
                                                {{ $scale->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('grade_scale_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="exam_date" class="form-label">Exam Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('exam_date') is-invalid @enderror"
                                           id="exam_date" name="exam_date" value="{{ old('exam_date') }}" required>
                                    @error('exam_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="start_time" class="form-label">Start Time</label>
                                    <input type="time" class="form-control @error('start_time') is-invalid @enderror"
                                           id="start_time" name="start_time" value="{{ old('start_time') }}">
                                    @error('start_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="end_time" class="form-label">End Time</label>
                                    <input type="time" class="form-control @error('end_time') is-invalid @enderror"
                                           id="end_time" name="end_time" value="{{ old('end_time') }}">
                                    @error('end_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instructions</label>
                            <textarea class="form-control @error('instructions') is-invalid @enderror"
                                      id="instructions" name="instructions" rows="3">{{ old('instructions') }}</textarea>
                            @error('instructions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="target_classes" class="form-label">Target Classes <span class="text-danger">*</span></label>
                            <select class="form-select @error('target_classes') is-invalid @enderror"
                                    id="target_classes" name="target_classes[]" multiple required>
                                @foreach($classes ?? [] as $class)
                                    <option value="{{ $class->id }}" {{ in_array($class->id, old('target_classes', [])) ? 'selected' : '' }}>
                                        {{ $class->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('target_classes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Hold Ctrl/Cmd to select multiple classes</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="total_marks" class="form-label">Total Marks <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('total_marks') is-invalid @enderror"
                                           id="total_marks" name="total_marks" value="{{ old('total_marks') }}"
                                           min="1" step="0.01" required>
                                    @error('total_marks')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="passing_marks" class="form-label">Passing Marks <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('passing_marks') is-invalid @enderror"
                                           id="passing_marks" name="passing_marks" value="{{ old('passing_marks') }}"
                                           min="0" step="0.01" required>
                                    @error('passing_marks')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Subjects Section -->
                        <div class="mb-4">
                            <h6 class="font-weight-bold text-primary mb-3">Exam Subjects</h6>
                            <div id="subjects-container">
                                <div class="subject-item border rounded p-3 mb-3">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Subject <span class="text-danger">*</span></label>
                                                <select class="form-select" name="subjects[0][subject_id]" required>
                                                    <option value="">Select Subject</option>
                                                    @foreach($subjects ?? [] as $subject)
                                                        <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Teacher</label>
                                                <select class="form-select" name="subjects[0][teacher_id]">
                                                    <option value="">Select Teacher</option>
                                                    @foreach($teachers ?? [] as $teacher)
                                                        <option value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Total Marks <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control" name="subjects[0][subject_total_marks]"
                                                       min="1" step="0.01" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Passing Marks <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control" name="subjects[0][subject_passing_marks]"
                                                       min="0" step="0.01" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Start Time</label>
                                                <input type="time" class="form-control" name="subjects[0][subject_start_time]">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">End Time</label>
                                                <input type="time" class="form-control" name="subjects[0][subject_end_time]">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Duration (Minutes)</label>
                                                <input type="number" class="form-control" name="subjects[0][subject_duration_minutes]" min="1">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Subject Instructions</label>
                                        <textarea class="form-control" name="subjects[0][subject_instructions]" rows="2"></textarea>
                                    </div>
                                    <button type="button" class="btn btn-danger btn-sm remove-subject" style="display: none;">
                                        <i class="fas fa-trash me-1"></i> Remove Subject
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary btn-sm" id="add-subject">
                                <i class="fas fa-plus me-1"></i> Add Subject
                            </button>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.grading.exams.index') }}" class="btn btn-white">
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Create Exam
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i> Exam Guidelines</h6>
                        <ul class="mb-0 small">
                            <li>Choose a descriptive title for the exam</li>
                            <li>Use a unique exam code for identification</li>
                            <li>Select appropriate academic year and term</li>
                            <li>Choose the correct grade category</li>
                            <li>Set realistic duration and marks</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-1"></i> Important Notes</h6>
                        <ul class="mb-0 small">
                            <li>Exam code must be unique</li>
                            <li>Passing marks cannot exceed total marks</li>
                            <li>Published exams will be visible to students</li>
                            <li>Draft exams can be edited freely</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let subjectIndex = 1;

    // Auto-generate exam code from title
    $('#title').on('input', function() {
        let title = $(this).val();
        let code = title.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 10);
        $('#exam_code').val(code);
    });

    // Validate passing marks against total marks
    $('#total_marks, #passing_marks').on('input', function() {
        let totalMarks = parseFloat($('#total_marks').val()) || 0;
        let passingMarks = parseFloat($('#passing_marks').val()) || 0;

        if (passingMarks > totalMarks) {
            $('#passing_marks').addClass('is-invalid');
            $('#passing_marks').siblings('.invalid-feedback').text('Passing marks cannot exceed total marks');
        } else {
            $('#passing_marks').removeClass('is-invalid');
        }
    });

    // Add subject functionality
    $('#add-subject').on('click', function() {
        let subjectHtml = `
            <div class="subject-item border rounded p-3 mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Subject <span class="text-danger">*</span></label>
                            <select class="form-select" name="subjects[${subjectIndex}][subject_id]" required>
                                <option value="">Select Subject</option>
                                @foreach($subjects ?? [] as $subject)
                                    <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Teacher</label>
                            <select class="form-select" name="subjects[${subjectIndex}][teacher_id]">
                                <option value="">Select Teacher</option>
                                @foreach($teachers ?? [] as $teacher)
                                    <option value="{{ $teacher->id }}">{{ $teacher->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Total Marks <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" name="subjects[${subjectIndex}][subject_total_marks]"
                                   min="1" step="0.01" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Passing Marks <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" name="subjects[${subjectIndex}][subject_passing_marks]"
                                   min="0" step="0.01" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">Start Time</label>
                            <input type="time" class="form-control" name="subjects[${subjectIndex}][subject_start_time]">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">End Time</label>
                            <input type="time" class="form-control" name="subjects[${subjectIndex}][subject_end_time]">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">Duration (Minutes)</label>
                            <input type="number" class="form-control" name="subjects[${subjectIndex}][subject_duration_minutes]" min="1">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Subject Instructions</label>
                    <textarea class="form-control" name="subjects[${subjectIndex}][subject_instructions]" rows="2"></textarea>
                </div>
                <button type="button" class="btn btn-danger btn-sm remove-subject">
                    <i class="fas fa-trash me-1"></i> Remove Subject
                </button>
            </div>
        `;

        $('#subjects-container').append(subjectHtml);
        subjectIndex++;
        updateRemoveButtons();
    });

    // Remove subject functionality
    $(document).on('click', '.remove-subject', function() {
        $(this).closest('.subject-item').remove();
        updateRemoveButtons();
    });

    function updateRemoveButtons() {
        let subjectItems = $('.subject-item');
        if (subjectItems.length > 1) {
            $('.remove-subject').show();
        } else {
            $('.remove-subject').hide();
        }
    }

    // Initialize remove buttons
    updateRemoveButtons();
});
</script>
@endpush
