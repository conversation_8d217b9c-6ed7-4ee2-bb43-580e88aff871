<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\GradeScale;
use App\Models\GradeCategory;
use App\Models\GradeCommentTemplate;

class GradingSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Grade Scales
        $this->createGradeScales();
        
        // Create Grade Categories
        $this->createGradeCategories();
        
        // Create Grade Comment Templates
        $this->createGradeCommentTemplates();
    }

    private function createGradeScales(): void
    {
        // A-F Letter Grade Scale
        GradeScale::create([
            'name' => 'A-F Letter Grade Scale',
            'type' => 'letter',
            'scale_values' => [
                ['min' => 97, 'max' => 100, 'grade' => 'A+', 'points' => 4.0],
                ['min' => 93, 'max' => 96, 'grade' => 'A', 'points' => 4.0],
                ['min' => 90, 'max' => 92, 'grade' => 'A-', 'points' => 3.7],
                ['min' => 87, 'max' => 89, 'grade' => 'B+', 'points' => 3.3],
                ['min' => 83, 'max' => 86, 'grade' => 'B', 'points' => 3.0],
                ['min' => 80, 'max' => 82, 'grade' => 'B-', 'points' => 2.7],
                ['min' => 77, 'max' => 79, 'grade' => 'C+', 'points' => 2.3],
                ['min' => 73, 'max' => 76, 'grade' => 'C', 'points' => 2.0],
                ['min' => 70, 'max' => 72, 'grade' => 'C-', 'points' => 1.7],
                ['min' => 67, 'max' => 69, 'grade' => 'D+', 'points' => 1.3],
                ['min' => 63, 'max' => 66, 'grade' => 'D', 'points' => 1.0],
                ['min' => 60, 'max' => 62, 'grade' => 'D-', 'points' => 0.7],
                ['min' => 0, 'max' => 59, 'grade' => 'F', 'points' => 0.0],
            ],
            'is_default' => true,
            'is_active' => true,
        ]);

        // Percentage Scale
        GradeScale::create([
            'name' => 'Percentage Scale',
            'type' => 'percentage',
            'scale_values' => [
                ['min' => 90, 'max' => 100, 'grade' => 'Excellent', 'points' => 4.0],
                ['min' => 80, 'max' => 89, 'grade' => 'Very Good', 'points' => 3.0],
                ['min' => 70, 'max' => 79, 'grade' => 'Good', 'points' => 2.0],
                ['min' => 60, 'max' => 69, 'grade' => 'Satisfactory', 'points' => 1.0],
                ['min' => 0, 'max' => 59, 'grade' => 'Needs Improvement', 'points' => 0.0],
            ],
            'is_default' => false,
            'is_active' => true,
        ]);

        // Malaysian UPSR Scale
        GradeScale::create([
            'name' => 'Malaysian UPSR Scale',
            'type' => 'letter',
            'scale_values' => [
                ['min' => 90, 'max' => 100, 'grade' => 'A', 'points' => 4.0],
                ['min' => 80, 'max' => 89, 'grade' => 'B', 'points' => 3.0],
                ['min' => 65, 'max' => 79, 'grade' => 'C', 'points' => 2.0],
                ['min' => 50, 'max' => 64, 'grade' => 'D', 'points' => 1.0],
                ['min' => 0, 'max' => 49, 'grade' => 'E', 'points' => 0.0],
            ],
            'is_default' => false,
            'is_active' => true,
        ]);
    }

    private function createGradeCategories(): void
    {
        $categories = [
            [
                'name' => 'Quiz',
                'code' => 'QUIZ',
                'description' => 'Short assessments and pop quizzes',
                'weight_percentage' => 15.0,
                'color' => '#3b82f6',
                'sort_order' => 1,
            ],
            [
                'name' => 'Assignment',
                'code' => 'ASSIGN',
                'description' => 'Homework and take-home assignments',
                'weight_percentage' => 20.0,
                'color' => '#8b5cf6',
                'sort_order' => 2,
            ],
            [
                'name' => 'Test',
                'code' => 'TEST',
                'description' => 'Unit tests and chapter tests',
                'weight_percentage' => 25.0,
                'color' => '#f59e0b',
                'sort_order' => 3,
            ],
            [
                'name' => 'Project',
                'code' => 'PROJECT',
                'description' => 'Long-term projects and presentations',
                'weight_percentage' => 15.0,
                'color' => '#06b6d4',
                'sort_order' => 4,
            ],
            [
                'name' => 'Midterm Exam',
                'code' => 'MIDTERM',
                'description' => 'Mid-semester examinations',
                'weight_percentage' => 10.0,
                'color' => '#f97316',
                'sort_order' => 5,
            ],
            [
                'name' => 'Final Exam',
                'code' => 'FINAL',
                'description' => 'End-of-term final examinations',
                'weight_percentage' => 15.0,
                'color' => '#ef4444',
                'sort_order' => 6,
            ],
        ];

        foreach ($categories as $category) {
            GradeCategory::create($category);
        }
    }

    private function createGradeCommentTemplates(): void
    {
        $templates = [
            // Excellent Performance
            [
                'title' => 'Outstanding Achievement',
                'comment' => 'Demonstrates exceptional understanding and consistently produces high-quality work.',
                'category' => 'excellent',
                'grade_range' => ['min' => 95, 'max' => 100],
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Excellent Work',
                'comment' => 'Shows excellent grasp of concepts and applies knowledge effectively.',
                'category' => 'excellent',
                'grade_range' => ['min' => 90, 'max' => 94],
                'created_by' => 1,
                'is_public' => true,
            ],
            
            // Positive Performance
            [
                'title' => 'Good Progress',
                'comment' => 'Making good progress and showing solid understanding of the material.',
                'category' => 'positive',
                'grade_range' => ['min' => 80, 'max' => 89],
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Satisfactory Work',
                'comment' => 'Demonstrates satisfactory understanding with room for growth.',
                'category' => 'positive',
                'grade_range' => ['min' => 70, 'max' => 79],
                'created_by' => 1,
                'is_public' => true,
            ],
            
            // Needs Improvement
            [
                'title' => 'Needs Additional Support',
                'comment' => 'Would benefit from additional practice and support to strengthen understanding.',
                'category' => 'improvement',
                'grade_range' => ['min' => 60, 'max' => 69],
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Requires Intervention',
                'comment' => 'Requires immediate intervention and additional support to meet learning objectives.',
                'category' => 'concern',
                'grade_range' => ['min' => 0, 'max' => 59],
                'created_by' => 1,
                'is_public' => true,
            ],
            
            // General Comments
            [
                'title' => 'Active Participation',
                'comment' => 'Shows excellent participation and engagement in class activities.',
                'category' => 'positive',
                'grade_range' => null,
                'created_by' => 1,
                'is_public' => true,
            ],
            [
                'title' => 'Consistent Effort',
                'comment' => 'Demonstrates consistent effort and dedication to learning.',
                'category' => 'positive',
                'grade_range' => null,
                'created_by' => 1,
                'is_public' => true,
            ],
        ];

        foreach ($templates as $template) {
            GradeCommentTemplate::create($template);
        }
    }
}
