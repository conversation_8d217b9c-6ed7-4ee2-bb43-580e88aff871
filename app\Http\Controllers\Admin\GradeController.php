<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\Student;
use App\Models\Subject;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use App\Models\ClassRoom;
use App\Models\Exam;
use App\Models\Assignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GradeController extends Controller
{
    public function index(Request $request)
    {
        $query = Grade::with(['student', 'subject', 'academicYear', 'academicTerm', 'gradeCategory', 'exam', 'assignment']);

        // Apply filters
        if ($request->filled('academic_year_id')) {
            $query->where('academic_year_id', $request->academic_year_id);
        }

        if ($request->filled('academic_term_id')) {
            $query->where('academic_term_id', $request->academic_term_id);
        }

        if ($request->filled('grade_category_id')) {
            $query->where('grade_category_id', $request->grade_category_id);
        }

        if ($request->filled('subject_id')) {
            $query->where('subject_id', $request->subject_id);
        }

        if ($request->filled('class_room_id')) {
            $query->whereHas('student', function ($q) use ($request) {
                $q->where('class_room_id', $request->class_room_id);
            });
        }

        if ($request->filled('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        if ($request->filled('grade_type')) {
            $query->where('grade_type', $request->grade_type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('student', function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('student_id', 'like', "%{$search}%");
            });
        }

        $grades = $query->orderBy('created_at', 'desc')->paginate(15);

        // Statistics
        $stats = [
            'total' => Grade::count(),
            'this_term' => Grade::whereHas('academicTerm', function ($q) {
                $q->where('is_current', true);
            })->count(),
            'average_score' => Grade::avg('marks_obtained'),
            'pass_rate' => Grade::where('is_passed', true)->count() / max(Grade::count(), 1) * 100,
        ];

        // Filter options
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $classRooms = ClassRoom::where('is_active', true)->orderBy('name')->get();
        $students = Student::where('is_active', true)->orderBy('first_name')->get();

        return view('admin.grades.index', compact(
            'grades',
            'stats',
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'classRooms',
            'students'
        ));
    }

    public function create()
    {
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $students = Student::where('is_active', true)->orderBy('first_name')->get();
        $exams = Exam::where('status', 'completed')->orderBy('title')->get();
        $assignments = Assignment::where('status', 'active')->orderBy('title')->get();

        return view('admin.grades.create', compact(
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'students',
            'exams',
            'assignments'
        ));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'subject_id' => 'required|exists:subjects,id',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'grade_type' => 'required|in:exam,assignment,quiz,project,participation',
            'exam_id' => 'nullable|exists:exams,id',
            'assignment_id' => 'nullable|exists:assignments,id',
            'marks_obtained' => 'required|numeric|min:0',
            'total_marks' => 'required|numeric|min:0|gte:marks_obtained',
            'grade_letter' => 'nullable|string|max:5',
            'grade_point' => 'nullable|numeric|min:0|max:4',
            'remarks' => 'nullable|string',
            'is_published' => 'boolean',
        ]);

        // Calculate percentage and pass status
        $validated['percentage'] = ($validated['marks_obtained'] / $validated['total_marks']) * 100;
        $validated['is_passed'] = $validated['percentage'] >= 50; // Default passing percentage
        $validated['recorded_by'] = auth()->id();

        // Auto-generate grade letter and point if not provided
        if (!$validated['grade_letter']) {
            $validated['grade_letter'] = $this->calculateGradeLetter($validated['percentage']);
        }
        if (!$validated['grade_point']) {
            $validated['grade_point'] = $this->calculateGradePoint($validated['percentage']);
        }

        $grade = Grade::create($validated);

        return redirect()->route('admin.grading.grades.show', $grade)
            ->with('success', 'Grade recorded successfully.');
    }

    public function show(Grade $grade)
    {
        $grade->load(['student', 'subject', 'academicYear', 'academicTerm', 'gradeCategory', 'exam', 'assignment', 'recordedBy']);

        return view('admin.grades.show', compact('grade'));
    }

    public function edit(Grade $grade)
    {
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $students = Student::where('is_active', true)->orderBy('first_name')->get();
        $exams = Exam::where('status', 'completed')->orderBy('title')->get();
        $assignments = Assignment::where('status', 'active')->orderBy('title')->get();

        return view('admin.grades.edit', compact(
            'grade',
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'students',
            'exams',
            'assignments'
        ));
    }

    public function update(Request $request, Grade $grade)
    {
        $validated = $request->validate([
            'student_id' => 'required|exists:students,id',
            'subject_id' => 'required|exists:subjects,id',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'grade_type' => 'required|in:exam,assignment,quiz,project,participation',
            'exam_id' => 'nullable|exists:exams,id',
            'assignment_id' => 'nullable|exists:assignments,id',
            'marks_obtained' => 'required|numeric|min:0',
            'total_marks' => 'required|numeric|min:0|gte:marks_obtained',
            'grade_letter' => 'nullable|string|max:5',
            'grade_point' => 'nullable|numeric|min:0|max:4',
            'remarks' => 'nullable|string',
            'is_published' => 'boolean',
        ]);

        // Recalculate percentage and pass status
        $validated['percentage'] = ($validated['marks_obtained'] / $validated['total_marks']) * 100;
        $validated['is_passed'] = $validated['percentage'] >= 50;

        // Auto-generate grade letter and point if not provided
        if (!$validated['grade_letter']) {
            $validated['grade_letter'] = $this->calculateGradeLetter($validated['percentage']);
        }
        if (!$validated['grade_point']) {
            $validated['grade_point'] = $this->calculateGradePoint($validated['percentage']);
        }

        $grade->update($validated);

        return redirect()->route('admin.grading.grades.show', $grade)
            ->with('success', 'Grade updated successfully.');
    }

    public function destroy(Grade $grade)
    {
        $grade->delete();

        return redirect()->route('admin.grading.grades.index')
            ->with('success', 'Grade deleted successfully.');
    }

    public function togglePublished(Grade $grade)
    {
        $grade->update(['is_published' => !$grade->is_published]);

        return back()->with('success', 'Grade publication status updated successfully.');
    }

    public function bulkEntry()
    {
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $classRooms = ClassRoom::where('is_active', true)->orderBy('name')->get();
        $exams = Exam::where('status', 'completed')->orderBy('title')->get();
        $assignments = Assignment::where('status', 'active')->orderBy('title')->get();

        return view('admin.grades.bulk-entry', compact(
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'classRooms',
            'exams',
            'assignments'
        ));
    }

    public function storeBulk(Request $request)
    {
        $validated = $request->validate([
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'subject_id' => 'required|exists:subjects,id',
            'grade_type' => 'required|in:exam,assignment,quiz,project,participation',
            'exam_id' => 'nullable|exists:exams,id',
            'assignment_id' => 'nullable|exists:assignments,id',
            'total_marks' => 'required|numeric|min:0',
            'grades' => 'required|array',
            'grades.*.student_id' => 'required|exists:students,id',
            'grades.*.marks_obtained' => 'required|numeric|min:0',
            'grades.*.remarks' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated) {
            foreach ($validated['grades'] as $gradeData) {
                $percentage = ($gradeData['marks_obtained'] / $validated['total_marks']) * 100;
                
                Grade::create([
                    'student_id' => $gradeData['student_id'],
                    'subject_id' => $validated['subject_id'],
                    'academic_year_id' => $validated['academic_year_id'],
                    'academic_term_id' => $validated['academic_term_id'],
                    'grade_category_id' => $validated['grade_category_id'],
                    'grade_type' => $validated['grade_type'],
                    'exam_id' => $validated['exam_id'],
                    'assignment_id' => $validated['assignment_id'],
                    'marks_obtained' => $gradeData['marks_obtained'],
                    'total_marks' => $validated['total_marks'],
                    'percentage' => $percentage,
                    'grade_letter' => $this->calculateGradeLetter($percentage),
                    'grade_point' => $this->calculateGradePoint($percentage),
                    'is_passed' => $percentage >= 50,
                    'remarks' => $gradeData['remarks'] ?? null,
                    'recorded_by' => auth()->id(),
                    'is_published' => false,
                ]);
            }
        });

        return redirect()->route('admin.grading.grades.index')
            ->with('success', 'Grades recorded successfully.');
    }

    private function calculateGradeLetter($percentage)
    {
        if ($percentage >= 90) return 'A+';
        if ($percentage >= 80) return 'A';
        if ($percentage >= 70) return 'B+';
        if ($percentage >= 60) return 'B';
        if ($percentage >= 50) return 'C';
        if ($percentage >= 40) return 'D';
        return 'F';
    }

    private function calculateGradePoint($percentage)
    {
        if ($percentage >= 90) return 4.0;
        if ($percentage >= 80) return 3.5;
        if ($percentage >= 70) return 3.0;
        if ($percentage >= 60) return 2.5;
        if ($percentage >= 50) return 2.0;
        if ($percentage >= 40) return 1.0;
        return 0.0;
    }
}
