@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Grades</h1>
                <p class="text-gray-600">View your academic performance and grades</p>
            </div>
            <a href="{{ route('student.dashboard') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Coming Soon Notice -->
    <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-purple-800">Gradebook Coming Soon</h3>
                <div class="mt-2 text-sm text-purple-700">
                    <p>You'll be able to view:</p>
                    <ul class="mt-2 list-disc list-inside space-y-1">
                        <li>Current grades for all subjects</li>
                        <li>Assignment and test scores</li>
                        <li>Grade trends and progress</li>
                        <li>Teacher comments and feedback</li>
                        <li>Report cards and transcripts</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Info -->
    @php
        $student = auth()->user()->student;
    @endphp

    @if($student)
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Academic Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Current Class</h4>
                    <p class="text-lg text-gray-900">{{ $student->class }}</p>
                    <p class="text-sm text-gray-500">{{ $student->section }} Section</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Roll Number</h4>
                    <p class="text-lg text-gray-900">{{ $student->roll_number }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Academic Year</h4>
                    <p class="text-lg text-gray-900">{{ now()->year }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Placeholder Subjects -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Subjects</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @php
                    $subjects = ['Mathematics', 'Science', 'English', 'Bahasa Malaysia', 'History', 'Geography'];
                @endphp
                
                @foreach($subjects as $subject)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ $subject }}</h4>
                                <p class="text-xs text-gray-500">Current Term</p>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-medium text-gray-400">--</span>
                                <p class="text-xs text-gray-400">Grade</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="bg-gray-200 rounded-full h-2">
                                <div class="bg-gray-300 h-2 rounded-full" style="width: 0%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">No grades yet</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif


</div>
@endsection
